# API Configuration Management Guide

## Overview

This document describes the centralized API configuration system implemented in the AppyStoreMRecharge Android application. All API URLs and endpoints are now managed through a single configuration class to improve maintainability and reduce hardcoded values throughout the codebase.

## Configuration Class: `Config.java`

### Location
`com.appystore.mrecharge.app.Config`

### Key Features

1. **Centralized API URL Management**: All API endpoints are defined as constants
2. **Default URL Configuration**: Provides fallback URLs when no custom configuration is set
3. **Utility Methods**: Helper methods for URL construction and validation
4. **SharedPreferences Integration**: Consistent key naming for storing API configurations

## API Configuration Constants

### Primary API URLs

```java
// Default license authentication API URL
public static final String DEFAULT_LICENSE_API_URL = 
    "http://192.168.0.106/AppyStoreMRecharge/mrecharge_simsupport_telecom/License_of_Tel_beautiful_design/admin/api/device_auth.php";

// Default base domain for API services
public static final String DEFAULT_API_BASE_DOMAIN = "192.168.0.106/ashiktelecom";

// Default protocol
public static final String DEFAULT_API_PROTOCOL = "http";
```

### API Endpoint Paths

```java
// Device registration endpoint
public static final String ENDPOINT_DEVICE_REGISTRATION = "index.php/Modemcon/device";

// Service request endpoint (for recharge requests)
public static final String ENDPOINT_SERVICE_REQUEST = "index.php/Modemcon/request";

// Response update endpoint
public static final String ENDPOINT_RESPONSE_UPDATE = "index.php/Modemcon/updateres";

// Message update endpoint
public static final String ENDPOINT_MESSAGE_UPDATE = "index.php/Modemcon/update";
```

### SharedPreferences Keys

```java
// Key for storing custom API URL
public static final String PREF_KEY_API_URL = "api_url";

// Key for storing API domain
public static final String PREF_KEY_API_DOMAIN = "url";

// Key for storing API protocol (http/https)
public static final String PREF_KEY_API_PROTOCOL = "sec";
```

## Updated Components

### 1. Retrofit API Interfaces

All Retrofit interfaces now use centralized endpoint constants:

- **WeatherAPIs.java**: Uses `Config.ENDPOINT_SERVICE_REQUEST`
- **Devicer.java**: Uses `Config.ENDPOINT_DEVICE_REGISTRATION`
- **Responseupdate.java**: Uses `Config.ENDPOINT_RESPONSE_UPDATE`
- **Msgupdate.java**: Uses `Config.ENDPOINT_MESSAGE_UPDATE`

### 2. MainActivity.java

- Updated `getApiUrl()` method to use `Config.getDefaultLicenseApiUrl()`
- Uses `Config.PREF_KEY_API_URL` for SharedPreferences key

### 3. Service Classes

- **sever.java**: Uses `Config.buildApiBaseUrl()` for URL construction
- **Dialfunction.java**: Uses `Config.buildApiBaseUrl()` for URL construction

## Utility Methods

### `buildApiBaseUrl(String protocol, String domain)`

Constructs a complete API base URL from protocol and domain components.

**Features:**
- Handles null/empty parameters with defaults
- Automatically removes legacy `/mitload` paths
- Returns properly formatted URL

**Example:**
```java
String baseUrl = Config.buildApiBaseUrl("http", "192.168.0.106/ashiktelecom");
// Returns: "http://192.168.0.106/ashiktelecom"
```

### `getDefaultLicenseApiUrl()`

Returns the default license authentication API URL.

**Example:**
```java
String licenseUrl = Config.getDefaultLicenseApiUrl();
// Returns: "http://192.168.0.106/AppyStoreMRecharge/mrecharge_simsupport_telecom/License_of_Tel_beautiful_design/admin/api/device_auth.php"
```

## Usage Examples

### Getting API URL in Activities

```java
// In MainActivity or other activities
private String getApiUrl() {
    String defaultApiUrl = Config.getDefaultLicenseApiUrl();
    String savedApiUrl = PreferenceManager.getDefaultSharedPreferences(getApplicationContext())
            .getString(Config.PREF_KEY_API_URL, null);
    
    if (savedApiUrl == null || savedApiUrl.isEmpty()) {
        SavePreferences(Config.PREF_KEY_API_URL, defaultApiUrl);
        return defaultApiUrl;
    }
    return savedApiUrl;
}
```

### Building API Base URLs in Services

```java
// In service classes
String protocol = MainActivity.getPref(Config.PREF_KEY_API_PROTOCOL, getApplicationContext());
String domain = MainActivity.getPref(Config.PREF_KEY_API_DOMAIN, getApplicationContext());
String baseUrl = Config.buildApiBaseUrl(protocol, domain);
```

### Using Retrofit Interfaces

```java
// Create API service with centralized endpoints
WeatherAPIs apiService = NetworkClient.getRetrofitClient(baseUrl).create(WeatherAPIs.class);
// The endpoint path is automatically handled by Config.ENDPOINT_SERVICE_REQUEST
```

## Benefits

1. **Centralized Management**: All API URLs are managed in one location
2. **Easy Maintenance**: URL changes only require updates in the Config class
3. **Consistency**: Standardized naming conventions and key usage
4. **Flexibility**: Easy to switch between different environments
5. **Legacy Support**: Automatic handling of legacy URL formats

## Migration Notes

- All hardcoded URLs have been replaced with Config class references
- SharedPreferences keys are now standardized through Config constants
- Legacy `/mitload` path handling is centralized in the `buildApiBaseUrl()` method
- Retrofit interfaces use compile-time constants for endpoint paths

## Future Enhancements

1. **Environment Configuration**: Add support for development, staging, and production environments
2. **Dynamic URL Updates**: Implement runtime URL configuration updates
3. **URL Validation**: Add comprehensive URL validation methods
4. **Configuration Backup**: Implement configuration export/import functionality

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure `import com.appystore.mrecharge.app.Config;` is added to files using Config
2. **Compilation Errors**: Clean and rebuild the project after making configuration changes
3. **Runtime Errors**: Verify that SharedPreferences keys match Config constants

### Debugging

Enable logging to track API URL usage:
```java
Log.d("API_CONFIG", "Using API endpoint: " + Config.getDefaultLicenseApiUrl());
Log.d("API_CONFIG", "Base URL: " + Config.buildApiBaseUrl(protocol, domain));
```
