package com.appystore.mrecharge.service;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper; // Import if handler needs explicit Looper
import android.os.SystemClock;
import android.preference.PreferenceManager;
// import android.telecom.TelecomManager; // Removed as getAccountHandles was removed
import android.telephony.SmsManager;
import android.util.Log; // Added for logging errors
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import androidx.work.WorkRequest;

import com.appystore.mrecharge.DbHelper;
import com.appystore.mrecharge.Dialfunction;
import com.appystore.mrecharge.NetworkClient;
import com.appystore.mrecharge.R;
import com.appystore.mrecharge.WResponse;
import com.appystore.mrecharge.WeatherAPIs;
import com.appystore.mrecharge.activity.MainActivity;
import com.appystore.mrecharge.app.Config;
import com.appystore.mrecharge.util.Msgupdate;
import com.google.android.gms.common.internal.ImagesContract;
// import com.google.firebase.messaging.Constants; // Removed as getAccountHandles was removed

import java.io.IOException;
import java.util.ArrayList;
import java.util.List; // Removed as getAccountHandles was removed

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class sever extends Service {

    private static final String TAG = "SeverService"; // Added for logging
    public static final String CHANNEL_ID = "ForegroundServiceChannel";
    String FinalJSonObject;
    boolean al = false;
    String alert;
    int busy = 0;
    String emid;
    boolean falgbd;
    boolean flag;
    boolean forward;
    private Handler handler; // Consider initializing with a background Looper if tasks are long
    /* access modifiers changed from: private */
    public DbHelper mydb;
    /* access modifiers changed from: private */
    public Dialfunction myser;
    String number;
    /* access modifiers changed from: private */
    public int runTime = 0;
    private Runnable runnable;
    SharedPreferences setting;
    String sf;
    int sms = 0;
    String smsnumber;
    String smstext;
    SharedPreferences sp;
    String title = "i";
    String url;
    String xemid;

    @Nullable // Added annotation
    @Override
    public IBinder onBind(Intent intent) {
        return null; // Standard for started services not providing binding
    }

    @Override
    public void onCreate() {
        super.onCreate(); // Call super.onCreate() first

        this.setting = getSharedPreferences("serv", Context.MODE_PRIVATE); // Use MODE_PRIVATE
        final SharedPreferences sharedPreferences = getSharedPreferences("pref", Context.MODE_PRIVATE); // Use MODE_PRIVATE
        this.runTime = Integer.parseInt(getPrefc("stime", "5", getApplicationContext())) * 1000;

        // Initialize DB and Dialer Helper here, not delayed, to ensure they exist when needed
        this.mydb = new DbHelper(getApplicationContext());
        this.myser = new Dialfunction(getApplicationContext());

        // Fix the domain path using Config class utility method
        String protocol = MainActivity.getPref(Config.PREF_KEY_API_PROTOCOL, getApplicationContext());
        String domain = MainActivity.getPref(Config.PREF_KEY_API_DOMAIN, getApplicationContext());
        this.url = Config.buildApiBaseUrl(protocol, domain);

        // Consider using a HandlerThread for background work instead of repeated Handler posts
        // on the main thread, especially with SystemClock.sleep involved.
        final Handler handler2 = new Handler(Looper.getMainLooper()); // Explicitly use main looper if UI interaction needed, otherwise consider background looper
        runnable = new Runnable() {
            @Override
            public void run() {
                // Update busy status from settings
                busy = setting.getInt("busy", 0);

                if (setting.getInt("stop", 0) == 1) {
                    // Check and potentially reset busy status if timeout occurred
                    if (busy == 1) {
                        long timeoutTime = setting.getLong("ttime", 0);
                        if (timeoutTime != 0 && System.currentTimeMillis() > timeoutTime) {
                            SharedPreferences.Editor edit = setting.edit();
                            edit.putInt("busy", 0);
                            edit.putLong("ttime", 0);
                            edit.apply(); // Use apply() for asynchronous save
                            busy = 0; // Update local variable too
                            Log.d(TAG, "Busy timeout reached, resetting busy flag.");
                        }
                    }

                    // Perform tasks only if not stopped
                    smsupdate();
                    if (!sharedPreferences.getBoolean("gateway_on", false)) {
                        checkdata();
                    }
                    playussd();
                    resupdate();
                } else {
                    Log.d(TAG, "Service processing stopped via settings.");
                }

                // Schedule the next run
                handler2.postDelayed(this, runTime);
            }
        };
        handler2.postDelayed(runnable, runTime); // Start the recurring task
        Log.d(TAG, "Service Created and recurring task scheduled.");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // Check if intent is null (can happen if service restarts)
        if (intent == null) {
            Log.w(TAG, "onStartCommand received null intent, possibly due to restart.");
            // Decide restart behavior based on START_STICKY - maybe re-read settings?
            // Let's just try to start foreground if channel exists
            createNotificationChannel();
            startForegroundWithNotification("Service Restarted");
            return Service.START_STICKY; // Correct constant
        }

        String inputExtra = intent.getStringExtra("inputExtra");
        if (inputExtra == null) {
            inputExtra = "Server running"; // Default text
        }

        // Handle stop command
        if ("off".equals(intent.getStringExtra("serv"))) {
            Log.d(TAG, "Stopping service via intent.");
            stopForeground(true);
            stopSelfResult(startId); // Use stopSelfResult
            // Cancel the handler callbacks
            if (handler != null && runnable != null) {
                handler.removeCallbacks(runnable);
            }
            return Service.START_NOT_STICKY; // Don't restart if explicitly stopped
        }

        // Create channel and start foreground
        createNotificationChannel();
        startForegroundWithNotification(inputExtra);


        // Return START_STICKY to restart the service if killed, but the intent won't be redelivered.
        // If you need the intent redelivered, use START_REDELIVER_INTENT.
        return Service.START_STICKY; // Correct constant
    }

    private void startForegroundWithNotification(String contentText) {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(this,
                0, notificationIntent, PendingIntent.FLAG_IMMUTABLE); // Use FLAG_IMMUTABLE

        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("Server Running")
                .setContentText(contentText)
                // TODO: Replace with a proper white/transparent status bar icon (e.g., ic_stat_notify.xml)
                .setSmallIcon(R.mipmap.ic_launcher) // <<<--- IMPORTANT: REPLACE THIS ICON
                .setContentIntent(pendingIntent)
                .setOngoing(true); // Ensure it's clear it's a foreground service

        startForeground(1, notificationBuilder.build());
        Log.d(TAG, "Service started in foreground.");
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        // Cancel the handler callbacks to prevent memory leaks
        if (handler != null && runnable != null) {
            handler.removeCallbacks(runnable);
        }
        // Unregister receivers if they were registered and long-lived
        // (The SMS receivers in sendSMS are short-lived, context-registered, maybe ok)
        Log.d(TAG, "Service Destroyed.");
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel serviceChannel = new NotificationChannel(
                    CHANNEL_ID,
                    "Foreground Service Channel",
                    NotificationManager.IMPORTANCE_LOW // Correct Constant (Low importance is usually good for foreground services)
            );
            // Optional: Add description, disable sound/vibration etc.
            serviceChannel.setDescription("Channel for Appystore Foreground Service");
            serviceChannel.setSound(null, null); // No sound
            serviceChannel.enableVibration(false); // No vibration

            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(serviceChannel);
                Log.d(TAG, "Notification channel created.");
            } else {
                Log.e(TAG, "NotificationManager was null, could not create channel.");
            }
        }
    }

    /* access modifiers changed from: private */
    private void smsupdate() {
        Cursor fetch = null; // Initialize cursor
        try { // Use try-finally to ensure cursor is closed
            fetch = this.mydb.fetch();
            if (fetch != null && fetch.moveToFirst()) {
                // Get column indices *once* and check if they exist
                int idColIndex = fetch.getColumnIndex(DbHelper.CONTACTS_COLUMN_ID);
                int senderColIndex = fetch.getColumnIndex("sender");
                int bodyColIndex = fetch.getColumnIndex("body");

                // Check if all columns exist before accessing
                if (idColIndex >= 0 && senderColIndex >= 0 && bodyColIndex >= 0) {
                    int dbId = fetch.getInt(idColIndex);
                    String sender = fetch.getString(senderColIndex);
                    String body = fetch.getString(bodyColIndex);

                    Log.d(TAG, "Processing SMS from DB ID: " + dbId + ", Sender: " + sender);
                    Toast.makeText(getApplicationContext(), "Processing SMS: " + body, Toast.LENGTH_SHORT).show(); // Use constant

                    runsmsupdate(this.url, sender, body, dbId);

                    // Warning: SystemClock.sleep blocks the thread it's called on.
                    // If this runs on the main thread, it can cause ANRs. Consider alternatives.
                    SystemClock.sleep(2000);

                } else {
                    // Log an error if columns are missing
                    Log.e(TAG, "One or more columns missing in smsupdate cursor! id:" + idColIndex + " sender:" + senderColIndex + " body:" + bodyColIndex);
                    // Maybe delete the problematic row?
                    // if (idColIndex >= 0) mydb.deletesms(fetch.getInt(idColIndex));
                }
            } else {
                //Log.d(TAG, "No pending SMS updates found in DB.");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in smsupdate", e);
        } finally {
            if (fetch != null && !fetch.isClosed()) {
                fetch.close(); // Always close the cursor
            }
        }
    }


    /* access modifiers changed from: private */
    private void playussd() {
        Cursor fetchlast = null;
        try {
            fetchlast = this.mydb.fetchlast();
            if (fetchlast != null && fetchlast.moveToFirst()) {
                int orderIdCol = fetchlast.getColumnIndex("orderid");
                int ussdCol = fetchlast.getColumnIndex("ussd");
                int slotCol = fetchlast.getColumnIndex("slot");
                int idCol = fetchlast.getColumnIndex(DbHelper.CONTACTS_COLUMN_ID);

                if (orderIdCol >= 0 && ussdCol >= 0 && slotCol >= 0 && idCol >= 0) {
                    String orderid = fetchlast.getString(orderIdCol);
                    String ussdCode = fetchlast.getString(ussdCol);
                    String slotStr = fetchlast.getString(slotCol);
                    String dbId = fetchlast.getString(idCol);
                    int slot = 0; // Default slot

                    try {
                        slot = Integer.parseInt(slotStr);
                    } catch (NumberFormatException e) {
                        Log.w(TAG, "Invalid slot number format: " + slotStr + " for order ID: " + orderid + ". Using default slot 0.");
                        slot = 0; // Fallback to default
                    }

                    // Check if the service is busy BEFORE attempting to dial
                    if (this.setting.getInt("busy", 0) == 0) {
                        Log.d(TAG, "Dialing USSD: " + ussdCode + " for order ID: " + orderid + " on slot: " + slot);
                        long timeoutTime = System.currentTimeMillis() + WorkRequest.DEFAULT_BACKOFF_DELAY_MILLIS; // 30 seconds
                        SharedPreferences.Editor edit = this.setting.edit();
                        edit.putLong("ttime", timeoutTime);
                        // Consider setting busy *before* dialing
                        // edit.putInt("busy", 1);
                        edit.apply();

                        SavePreferences("main_id", dbId); // Save the DB ID being processed
                        this.myser.dialUp(ussdCode, slot, orderid); // This likely sets busy = 1 internally or via broadcast
                    } else {
                        Log.d(TAG, "Service is busy, skipping USSD dialing for order ID: " + orderid);
                    }
                } else {
                    Log.e(TAG, "One or more columns missing in playussd cursor! orderid:" + orderIdCol + " ussd:" + ussdCol + " slot:" + slotCol + " id:" + idCol);
                }

            } else {
                //Log.d(TAG, "No pending USSD commands found in DB.");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in playussd", e);
        } finally {
            if (fetchlast != null && !fetchlast.isClosed()) {
                fetchlast.close();
            }
        }
    }


    /* access modifiers changed from: private */
    public void checkdata() {
        if(isNetworkAvailable()) {
            Log.d(TAG, "Network available, checking for data/commands.");
            fetchWeatherDetails(this.url);
        } else {
            Log.w(TAG, "Network not available, skipping checkdata.");
        }
    }

    private void runsmsupdate(String baseUrl, String sender, String body, final int dbId) {
        if (baseUrl == null || baseUrl.isEmpty()) {
            Log.e(TAG, "Base URL is null or empty in runsmsupdate");
            return;
        }

        Log.d(TAG, "Sending SMS update to server for DB ID: " + dbId);
        
        // Get the server URL and PIN from preferences
        String serverUrl = MainActivity.getPref(ImagesContract.URL, getApplicationContext());
        String pin = MainActivity.getPref("pin", getApplicationContext());
        
        // Log the request details
        Log.d(TAG, "Server URL: " + serverUrl);
        Log.d(TAG, "Sender: " + sender);
        Log.d(TAG, "Message length: " + (body != null ? body.length() : 0));
        
        // Create the API call
        Call<ResponseBody> call = NetworkClient.getRetrofitClient(baseUrl)
                .create(Msgupdate.class)
                .updateSms(pin, sender, body, "android_app");
        
        // Execute the call asynchronously
        call.enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                try {
                    if (response.isSuccessful() && response.body() != null) {
                        // Success case
                        String responseBody = response.body().string();
                        Log.d(TAG, "SMS update successful for DB ID: " + dbId + ", Response: " + responseBody);
                        
                        // Check if the response indicates success
                        if (responseBody.trim().equalsIgnoreCase("suc") || 
                            responseBody.contains("\"status\":1")) {
                            // Delete the SMS from local DB only if server confirms success
                            mydb.deletesms(dbId);
                            Log.d(TAG, "Deleted SMS record from local DB, ID: " + dbId);
                            
                            // Clear any retry count for this message
                            clearRetryCount(dbId);
                        } else {
                            Log.w(TAG, "Server did not confirm success for DB ID: " + dbId + 
                                  ", Response: " + responseBody);
                            // Schedule a retry
                            scheduleRetrySmsUpdate(baseUrl, sender, body, dbId);
                        }
                    } else {
                        // Handle error response
                        String errorBody = response.errorBody() != null ? 
                                response.errorBody().string() : "No error body";
                        Log.e(TAG, "Server error for DB ID: " + dbId + 
                              ", Code: " + response.code() + 
                              ", Error: " + errorBody);
                        
                        // If it's a 500 error, we'll retry later
                        if (response.code() == 500) {
                            Log.w(TAG, "Server error 500, will retry later for DB ID: " + dbId);
                            scheduleRetrySmsUpdate(baseUrl, sender, body, dbId);
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error processing SMS update response for DB ID: " + dbId, e);
                    scheduleRetrySmsUpdate(baseUrl, sender, body, dbId);
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                Log.e(TAG, "Network failure during SMS update for DB ID: " + dbId, t);
                
                // Implement retry logic here
                if (!call.isCanceled()) {
                    // Only retry for certain types of failures
                    if (t instanceof IOException) {
                        Log.w(TAG, "Network error, will retry later for DB ID: " + dbId);
                        // Schedule a retry
                        scheduleRetrySmsUpdate(baseUrl, sender, body, dbId);
                    }
                }
            }
        });
    }
    
    // Helper method to schedule retry for failed SMS updates
    private void scheduleRetrySmsUpdate(String baseUrl, String sender, String body, int dbId) {
        // Get current retry count from shared preferences
        SharedPreferences prefs = getSharedPreferences("sms_retry", Context.MODE_PRIVATE);
        int retryCount = prefs.getInt("retry_" + dbId, 0);
        
        // Maximum number of retries
        final int MAX_RETRIES = 3;
        
        if (retryCount < MAX_RETRIES) {
            // Calculate delay with exponential backoff (e.g., 5s, 10s, 20s)
            long delay = (long) (5000 * Math.pow(2, retryCount));
            
            // Increment retry count
            prefs.edit().putInt("retry_" + dbId, retryCount + 1).apply();
            
            Log.d(TAG, "Scheduling retry #" + (retryCount + 1) + " in " + delay + "ms for DB ID: " + dbId);
            
            // Schedule the retry
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                Log.d(TAG, "Executing retry #" + (retryCount + 1) + " for DB ID: " + dbId);
                runsmsupdate(baseUrl, sender, body, dbId);
            }, delay);
        } else {
            Log.e(TAG, "Max retries (" + MAX_RETRIES + ") reached for DB ID: " + dbId + ". Giving up.");
            // Clear the retry count to avoid affecting future messages
            clearRetryCount(dbId);
        }
    }
    
    // Helper method to clear retry count for a message
    private void clearRetryCount(int dbId) {
        SharedPreferences prefs = getSharedPreferences("sms_retry", Context.MODE_PRIVATE);
        if (prefs.contains("retry_" + dbId)) {
            prefs.edit().remove("retry_" + dbId).apply();
            Log.d(TAG, "Cleared retry count for DB ID: " + dbId);
        }
    }

    private void fetchWeatherDetails(String baseUrl) {
        if (baseUrl == null || baseUrl.isEmpty()) {
            Log.e(TAG, "Base URL is null or empty in fetchWeatherDetails");
            return;
        }

        // Log the exact URL and parameters being used
        String fullUrl = baseUrl + "/index.php/Modemcon/request";
        Log.d(TAG, "Fetching details from server...");
        Log.d(TAG, "Base URL: " + baseUrl);
        Log.d(TAG, "Full endpoint URL: " + fullUrl);
        Log.d(TAG, "POST parameters: m1=" + MainActivity.getPref("m1", getApplicationContext()) +
                  ", m2=" + MainActivity.getPref("m2", getApplicationContext()) +
                  ", m3=" + MainActivity.getPref("m3", getApplicationContext()) +
                  ", m4=" + MainActivity.getPref("m4", getApplicationContext()) +
                  ", m5=" + MainActivity.getPref("m5", getApplicationContext()) +
                  ", m6=" + MainActivity.getPref("m6", getApplicationContext()) +
                  ", m7=" + MainActivity.getPref("m7", getApplicationContext()) +
                  ", m8=" + MainActivity.getPref("m8", getApplicationContext()) +
                  ", pin=" + MainActivity.getPref("pin", getApplicationContext()) +
                  ", myid=" + MainActivity.getPref("myid", getApplicationContext()));

        ((WeatherAPIs) NetworkClient.getRetrofitClient(baseUrl).create(WeatherAPIs.class))
                .getWeatherByCity(
                        MainActivity.getPref("m1", getApplicationContext()),
                        MainActivity.getPref("m2", getApplicationContext()),
                        MainActivity.getPref("m3", getApplicationContext()),
                        MainActivity.getPref("m4", getApplicationContext()),
                        MainActivity.getPref("m5", getApplicationContext()),
                        MainActivity.getPref("m6", getApplicationContext()),
                        MainActivity.getPref("m7", getApplicationContext()),
                        MainActivity.getPref("m8", getApplicationContext()),
                        "refot", // Parameter name seems fixed
                        MainActivity.getPref("pin", getApplicationContext()),
                        this.setting.getInt("busy", 0), // Send current busy state
                        MainActivity.getPref("myid", getApplicationContext())
                ).enqueue(new Callback<WResponse>() { // Specify Type
                    @Override
                    public void onResponse(Call<WResponse> call, Response<WResponse> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            Log.d(TAG,"Received response from server.");
                            WResponse wResponse = response.body();

                            // Ensure status is not null before checking its value
                            if (wResponse.getstatus() != null && wResponse.getstatus() == 0) {
                                Log.d(TAG,"Server response status is 0, processing command.");
                                // Clear prefs (Consider if this is always needed on status 0)
                                SavePreferences("firstd", "8888888888888888888888888888888888"); // Placeholder?
                                ClearPreferences("1st"); ClearPreferences("2nd"); ClearPreferences("3rd");
                                ClearPreferences("4rd"); ClearPreferences("5th"); // Typo: 4th?
                                ClearPreferences("s1st"); ClearPreferences("s2nd"); ClearPreferences("s3rd");
                                ClearPreferences("s4rd"); ClearPreferences("s5th"); // Typo: s4th?
                                ClearPreferences("pcode");

                                // Reset flags in settings
                                SharedPreferences.Editor edit = setting.edit();
                                edit.putInt("enter", 0);
                                edit.putInt("triger", 0); // Corrected spelling? trigger?
                                edit.apply(); // Use apply

                                // Process response data safely, checking for nulls
                                number = wResponse.getnumber(); // Assuming these return String or null
                                String balance = wResponse.getbalance();
                                String pcode = wResponse.getpcode(); // Might be command type?
                                title = wResponse.gettitle();
                                String idVar = wResponse.getid(); // Order ID
                                String ussd = wResponse.getussd();
                                smstext = wResponse.getsmstext();

                                // Handle null Integer values before calling intValue()
                                int slot = (wResponse.getslot() != null) ? wResponse.getslot() : 0;
                                int auto = (wResponse.getauto() != null) ? wResponse.getauto() : 0;
                                sms = (wResponse.getsms() != null) ? wResponse.getsms() : 0; // 0 = USSD, 1 = SMS?
                                int line = (wResponse.getline() != null) ? wResponse.getline() : 0;
                                int trigger = (wResponse.gettriger() != null) ? wResponse.gettriger() : 0; // spelling?
                                int powerload = (wResponse.getpowerload() != null) ? wResponse.getpowerload() : 0;
                                int status = (wResponse.getstatus() != null) ? wResponse.getstatus() : -1; // Status already checked, but be safe
                                int resend = (wResponse.getResend() != null) ? wResponse.getResend() : 0;

                                if (idVar != null) SavePreferences("pcode", idVar); // Store the order ID

                                // Forwarding logic
                                forward = false; // Reset forwarding flag
                                String bkw = getPrefc("bkw", "01", getApplicationContext());
                                String rkw = getPrefc("rkw", "01", getApplicationContext());
                                String ngw = getPrefc("ngw", "01", getApplicationContext());

                                if (pcode != null) { // Check pcode is not null
                                    if (pcode.contains(bkw)) {
                                        forward = true;
                                        smsnumber = getPrefc("bkash", "01", getApplicationContext());
                                    } else if (pcode.contains(rkw)) {
                                        forward = true;
                                        smsnumber = getPrefc("rocket", "01", getApplicationContext());
                                    } else if (pcode.contains(ngw)) {
                                        forward = true;
                                        smsnumber = getPrefc("nogad", "01", getApplicationContext());
                                    }
                                }

                                if (title != null && title.contains("resend")) {
                                    // Handle Resend Logic
                                    if (number != null && balance != null && !mydb.check_data_resend(number, balance)) {
                                        Log.w(TAG, "Duplicate Resend Request detected for number: " + number);
                                        alert = "Duplicate Resend Request";
                                        // Flag handling seems complex, simplify or clarify intent
                                        // al = true;
                                        // flag = false;
                                        Toast.makeText(getBaseContext(), alert, Toast.LENGTH_SHORT).show();
                                    } else {
                                        Log.d(TAG,"Processing Resend Request.");
                                        // flag = true; // What is 'flag' for?
                                        // Insert into DB if it's a valid resend? Check logic.
                                        // The original code only checked, didn't insert resends.
                                    }
                                } else {
                                    // Handle Normal Request Logic
                                    if (number != null && balance != null && !mydb.check_data(number, balance)) {
                                        Log.w(TAG, "Duplicate Request detected for number: " + number);
                                        alert = "Duplicate Request";
                                        Toast.makeText(getBaseContext(), alert, Toast.LENGTH_SHORT).show();
                                        // al = true; // Avoid complex flag logic if possible
                                        // flag = false;
                                        return; // Stop processing if duplicate
                                    }

                                    // Proceed if not duplicate
                                    Log.d(TAG,"Processing new request for number: " + number);
                                    // flag = true; // Reset flag?

                                    // Check command type (pcode)
                                    if (pcode != null && pcode.contains("SMS")) {
                                        if (number != null && smstext != null) {
                                            Log.d(TAG, "Command type is SMS, sending SMS to: " + number);
                                            sendSMS(number, smstext);
                                        } else {
                                            Log.w(TAG, "Cannot send SMS, number or smstext is null.");
                                        }
                                    } else if (pcode != null && !pcode.equals("SMS")) { // Assume USSD if not explicitly SMS
                                        // Insert into DB for USSD processing
                                        if (idVar != null && number != null && balance != null && ussd != null) {
                                            Log.d(TAG, "Inserting USSD command into DB for order ID: " + idVar);
                                            mydb.insertContact(idVar, number, balance, ussd, pcode, line,
                                                    wResponse.getfirst(), wResponse.getsecond(), wResponse.getthree(), // Pass potentially null Strings
                                                    wResponse.getfour(), wResponse.getfive(),
                                                    trigger, slot, powerload, status, resend);

                                            // Handle initial resend status if needed
                                            if (resend != 0) {
                                                mydb.updateresend(idVar, "ostatus", "0", resend, line);
                                            }
                                        } else {
                                            Log.e(TAG, "Cannot insert USSD command, required data is missing.");
                                        }
                                    }

                                    // Handle SMS forwarding based on flags
                                    if (sms == 0 && forward && smsnumber != null && pcode != null && number != null && balance != null) {
                                        Log.d(TAG, "Forwarding request via SMS to: " + smsnumber);
                                        sendSMS(smsnumber, pcode + "*" + number + "*" + balance);
                                    }
                                }
                                // Reset flags?
                                // flag = false;
                                // al = false;

                            } else {
                                // Status was not 0 - check if it's status 4 (no pending requests)
                                Integer status = wResponse.getstatus();
                                if (status != null && status == 4) {
                                    Log.d(TAG, "Server response: No pending recharge requests (status 4) - this is normal");
                                } else {
                                    Log.d(TAG, "Server response status is not 0. Status: " + (status != null ? status : "null"));
                                    // Log additional response details for debugging
                                    Log.d(TAG, "Response details - ID: " + wResponse.getid() +
                                              ", Title: " + wResponse.gettitle() +
                                              ", Number: " + wResponse.getnumber());
                                }
                            }
                        } else {
                            // Response was not successful or body was null
                            if (response.code() == 200) {
                                // HTTP 200 but body is null - likely a JSON parsing issue
                                Log.w(TAG, "HTTP 200 received but response body is null - possible JSON parsing issue");
                                Log.w(TAG, "This usually means the server response format doesn't match the WResponse model");

                                // Try to get the raw response for debugging
                                try {
                                    if (response.raw() != null && response.raw().body() != null) {
                                        // Note: This might not work as expected since the response stream may be consumed
                                        Log.d(TAG, "Raw response available for debugging");
                                    }
                                } catch (Exception e) {
                                    Log.w(TAG, "Could not access raw response", e);
                                }
                            } else {
                                // Actual HTTP error
                                Log.e(TAG, "HTTP error from server. Code: " + response.code());
                                Log.e(TAG, "Request URL was: " + baseUrl + "/index.php/Modemcon/request");
                                Log.e(TAG, "Response message: " + response.message());

                                // Try to read error body for more details
                                try {
                                    if (response.errorBody() != null) {
                                        String errorBody = response.errorBody().string();
                                        Log.e(TAG, "Error response body: " + errorBody);
                                    }
                                } catch (Exception e) {
                                    Log.e(TAG, "Could not read error body", e);
                                }
                            }
                        }
                    }

                    @Override
                    public void onFailure(Call<WResponse> call, Throwable t) {
                        Log.e(TAG, "Network failure during fetchWeatherDetails", t);
                        // Network failures are typically connectivity issues, not server problems
                    }
                });
    }



    // --- SMS Sending ---
    // Note: Consider moving SMS sending logic to a separate utility class.
    // Requires SEND_SMS permission in Manifest.
    // Runtime permission request needed for Android 6.0+.
    public void sendSMS(String phoneNumber, String message) {
        if (phoneNumber == null || message == null || phoneNumber.isEmpty() || message.isEmpty()) {
            Log.e(TAG, "Cannot send SMS, phone number or message is empty.");
            return;
        }
        Log.d(TAG, "Attempting to send SMS to " + phoneNumber);
        try {
            SmsManager smsManager = SmsManager.getDefault();
            ArrayList<String> parts = smsManager.divideMessage(message);
            int numParts = parts.size();

            ArrayList<PendingIntent> sentIntents = new ArrayList<>();
            ArrayList<PendingIntent> deliveredIntents = new ArrayList<>();

            // Use unique request codes if multiple SMS are sent concurrently
            // For simplicity here, using 0, but could lead to collisions.
            // Use FLAG_IMMUTABLE or FLAG_MUTABLE explicitly based on need (IMMUTABLE preferred)
            PendingIntent sentPI = PendingIntent.getBroadcast(this, 0, new Intent("SMS_SENT"), PendingIntent.FLAG_IMMUTABLE);
            PendingIntent deliveredPI = PendingIntent.getBroadcast(this, 0, new Intent("SMS_DELIVERED"), PendingIntent.FLAG_IMMUTABLE);


            for (int i = 0; i < numParts; i++) {
                sentIntents.add(sentPI); // Can reuse the same PI for multipart
                deliveredIntents.add(deliveredPI); // Can reuse the same PI for multipart
            }

            // Register receivers temporarily (Consider managing their lifecycle better if needed)
            registerReceiver(smsSentReceiver, new IntentFilter("SMS_SENT"));
            registerReceiver(smsDeliveredReceiver, new IntentFilter("SMS_DELIVERED"));

            smsManager.sendMultipartTextMessage(phoneNumber, null, parts, sentIntents, deliveredIntents);

        } catch (Exception e) {
            Log.e(TAG, "Error sending SMS to " + phoneNumber, e);
            Toast.makeText(getBaseContext(), "SMS sending failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
            // Clean up receivers if registration happened before exception
            try { unregisterReceiver(smsSentReceiver); } catch (IllegalArgumentException iae) { /* ignore */ }
            try { unregisterReceiver(smsDeliveredReceiver); } catch (IllegalArgumentException iae) { /* ignore */ }
        }
    }

    // Define BroadcastReceivers as member variables to unregister them
    private final BroadcastReceiver smsSentReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String message;
            switch (getResultCode()) {
                case MainActivity.RESULT_OK: // Correct constant name
                    message = "SMS sent successfully.";
                    break;
                case SmsManager.RESULT_ERROR_GENERIC_FAILURE:
                    message = "SMS send failed: Generic failure.";
                    break;
                case SmsManager.RESULT_ERROR_RADIO_OFF:
                    message = "SMS send failed: Radio off.";
                    break;
                case SmsManager.RESULT_ERROR_NULL_PDU:
                    message = "SMS send failed: Null PDU.";
                    break;
                case SmsManager.RESULT_ERROR_NO_SERVICE:
                    message = "SMS send failed: No service.";
                    break;
                default:
                    message = "SMS send result: Unknown error code " + getResultCode();
                    break;
            }
            Log.d(TAG, message);
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
            // Unregister receiver after use
            try { context.unregisterReceiver(this); } catch (IllegalArgumentException e) { Log.w(TAG, "Error unregistering smsSentReceiver", e); }
        }
    };

    private final BroadcastReceiver smsDeliveredReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String message;
            switch (getResultCode()) {
                case MainActivity.RESULT_OK: // Correct constant name
                    message = "SMS delivered successfully.";
                    break;
                case MainActivity.RESULT_CANCELED: // Correct constant name
                    message = "SMS not delivered.";
                    break;
                default:
                    message = "SMS delivery result: Unknown code " + getResultCode();
                    break;
            }
            Log.d(TAG, message);
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
            // Unregister receiver after use
            try { context.unregisterReceiver(this); } catch (IllegalArgumentException e) { Log.w(TAG, "Error unregistering smsDeliveredReceiver", e); }
        }
    };

    // --- Preferences ---
    // Consider creating a dedicated Preference utility class

    public void SavePreferencesint(String key, int value) {
        // Use application context to avoid potential leaks
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());
        SharedPreferences.Editor editor = preferences.edit();
        editor.putInt(key, value);
        editor.apply(); // Use apply() for asynchronous saving
    }

    public void SavePreferences(String key, String value) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(key, value);
        editor.apply();
    }

    public void ClearPreferences(String key) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());
        SharedPreferences.Editor editor = preferences.edit();
        editor.remove(key);
        editor.apply();
    }

    public static String getPrefc(String key, String defaultValue, Context context) {
        // Ensure context is not null
        if (context == null) return defaultValue;
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context.getApplicationContext());
        return preferences.getString(key, defaultValue);
    }

    /* access modifiers changed from: private */
    private void resupdate() {
        Cursor fetchresponsenew = null;
        try {
            fetchresponsenew = this.mydb.fetchresponsenew();
            if (fetchresponsenew != null && fetchresponsenew.moveToFirst()) {
                int orderIdCol = fetchresponsenew.getColumnIndex("orderid");
                int apiResponseCol = fetchresponsenew.getColumnIndex("apiresponse");

                if (orderIdCol >= 0 && apiResponseCol >= 0) {
                    String orderid = fetchresponsenew.getString(orderIdCol);
                    String apiresponse = fetchresponsenew.getString(apiResponseCol);

                    if (apiresponse != null && apiresponse.length() > 10) { // Check if response seems valid
                        Log.d(TAG,"Found response for order ID: " + orderid + ", sending update to server.");
                        this.myser.runresupdate(orderid, apiresponse); // Assumes Dialfunction handles API call

                        // Warning: Blocking sleep. Consider async handling.
                        SystemClock.sleep(2000);
                    } else {
                        Log.w(TAG,"API response for order ID " + orderid + " is null or too short, skipping update.");
                        // Maybe delete invalid response?
                        // mydb.deleteresponse(orderid); // Need a method like this in DbHelper
                    }
                } else {
                    Log.e(TAG, "One or more columns missing in resupdate cursor! orderid:" + orderIdCol + " apiresponse:" + apiResponseCol);
                }
            } else {
                // Log.d(TAG, "No new responses found in DB to update.");
            }
        } catch(Exception e) {
            Log.e(TAG, "Error during resupdate", e);
        }
        finally {
            if (fetchresponsenew != null && !fetchresponsenew.isClosed()) {
                fetchresponsenew.close();
            }
        }
    }

    // Removed getAccountHandles method as it required API 21+ and was unused.

    @SuppressWarnings("deprecation") // Suppress deprecation warning for getActiveNetworkInfo
    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE); // Use constant
        if (connectivityManager == null) {
            Log.e(TAG, "ConnectivityManager is null.");
            return false;
        }
        // getActiveNetworkInfo is deprecated in API 29.
        // For newer APIs, use NetworkCapabilities.
        // This implementation works for older APIs specified by minSdk 16.
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        boolean isAvailable = activeNetworkInfo != null && activeNetworkInfo.isConnected();
        // Log.d(TAG, "Network Available: " + isAvailable); // Can be verbose, uncomment if needed
        return isAvailable;
    }
}