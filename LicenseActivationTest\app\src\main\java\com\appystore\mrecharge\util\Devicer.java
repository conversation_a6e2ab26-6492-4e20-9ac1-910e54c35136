package com.appystore.mrecharge.util;

import com.appystore.mrecharge.WResponse;
import com.appystore.mrecharge.app.Config;
import retrofit2.Call;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

/**
 * Device registration interface
 * Uses centralized endpoint configuration from Config class
 */
public interface Devicer {
    @FormUrlEncoded
    @POST(Config.ENDPOINT_DEVICE_REGISTRATION)
    Call<WResponse> Devicer(@Field("m1") String str, @Field("m2") String str2, @Field("m3") String str3, @Field("m4") String str4, @Field("m5") String str5, @Field("m6") String str6, @Field("m7") String str7, @Field("m8") String str8, @Field("device") String str9, @Field("name") String str10);
}
