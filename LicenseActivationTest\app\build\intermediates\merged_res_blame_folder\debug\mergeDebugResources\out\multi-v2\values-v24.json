{"logs": [{"outputFile": "com.appystore.mrecharge.app-mergeDebugResources-45:/values-v24/values-v24.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\1735b8cdd7f63af0f6534fc72d24b693\\transformed\\appcompat-1.7.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,212", "endColumns": "156,134", "endOffsets": "207,342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\5ce42f50b8e9cd31ff8c0bdc3b462bc2\\transformed\\material-1.12.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3,4,5,6,9,12,15", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,140,239,326,427,626,839,1040", "endLines": "2,3,4,5,8,11,14,17", "endColumns": "84,98,86,100,10,10,10,10", "endOffsets": "135,234,321,422,621,834,1035,1250"}, "to": {"startLines": "4,5,6,7,8,11,14,17", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "347,432,531,618,719,918,1131,1332", "endLines": "4,5,6,7,10,13,16,19", "endColumns": "84,98,86,100,10,10,10,10", "endOffsets": "427,526,613,714,913,1126,1327,1542"}}]}]}