<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
    <layouts>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/footer_background.xml">
        <config>
          <theme>@style/Theme.AppyStoreMRecharge</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_main.xml">
        <config>
          <theme>@style/Theme.AppyStoreMRecharge</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/config.xml">
        <config>
          <theme>@style/Theme.AppyStoreMRecharge</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/custom_dialog.xml">
        <config>
          <theme>@style/Theme.AppyStoreMRecharge</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/forward.xml">
        <config>
          <theme>@style/Theme.AppyStoreMRecharge</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_todo.xml">
        <config>
          <theme>@style/Theme.AppyStoreMRecharge</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/license_activation.xml">
        <config>
          <theme>@style/Theme.AppyStoreMRecharge</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/listmain.xml">
        <config>
          <theme>@style/Theme.AppyStoreMRecharge</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/settingsd.xml">
        <config>
          <theme>@style/Theme.AppyStoreMRecharge</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/menu/menu_main.xml">
        <config>
          <theme>@style/Theme.AppyStoreMRecharge</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="58a51180-a572-4e6c-aca4-5bba629ffdc7" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=Default, isTemplate=false, identifier=serial=192.168.0.101:5555;connection=df44746e)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="valueResourceFile" />
        <option value="layoutResourceFile" />
        <option value="resourceFile" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="ProjectErrors" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2wZTz6RJlw3LNu55E0mKAEx7FgA" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Android App.app.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "com.google.services.firebase.aqiPopupShown": "true",
    "dart.analysis.tool.window.visible": "false",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_directory_selection": "C:/xampp/htdocs/AppyStoreMRecharge/app/src/main/res/drawable",
    "last_opened_file_path": "F:/Sqlite Database files",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.17",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "preferences.updates",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="com.appystore.mrecharge" />
      <recent name="com.appystore.mrecharge.activity" />
      <recent name="com.appystore.mrecharge.util" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable" />
      <recent name="C:\xampp\htdocs\AppyStoreMRecharge\app\src\main\res\drawable" />
      <recent name="C:\xampp\htdocs\AppyStoreMRecharge\app\src\main\res\raw" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="LicenseActivationTest.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="58a51180-a572-4e6c-aca4-5bba629ffdc7" name="Changes" comment="" />
      <created>1746248355613</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746248355613</updated>
    </task>
    <servers />
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="com.appystore.mrecharge">
          <value>
            <CheckInfo lastCheckTimestamp="1751030867302" />
          </value>
        </entry>
        <entry key="com.appystore.mrecharge.test">
          <value>
            <CheckInfo lastCheckTimestamp="1751030867302" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>