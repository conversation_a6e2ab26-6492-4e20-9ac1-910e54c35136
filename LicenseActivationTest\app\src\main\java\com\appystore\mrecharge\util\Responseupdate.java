package com.appystore.mrecharge.util;

import com.appystore.mrecharge.WResponse;
import com.appystore.mrecharge.app.Config;
import retrofit2.Call;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

/**
 * Response update interface
 * Uses centralized endpoint configuration from Config class
 */
public interface Responseupdate {
    @FormUrlEncoded
    @POST(Config.ENDPOINT_RESPONSE_UPDATE)
    Call<WResponse> getresupdate(@Field("pass") String str, @Field("sender") String str2, @Field("body") String str3, @Field("ref") String str4);
}
