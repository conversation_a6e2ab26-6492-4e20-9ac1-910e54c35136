package com.appystore.mrecharge;


import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Parcelable;
import android.preference.PreferenceManager;
import android.telecom.PhoneAccountHandle;
import android.telecom.TelecomManager;
import android.util.Log;
import android.widget.Toast;
import androidx.core.app.ActivityCompat;
import com.appystore.mrecharge.activity.MainActivity;
import com.appystore.mrecharge.app.Config;
import com.appystore.mrecharge.util.Responseupdate;
import com.google.android.gms.common.internal.ImagesContract;
import com.google.firebase.messaging.Constants;
import java.lang.reflect.Method;
import java.util.List;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class Dialfunction {
    /* access modifiers changed from: private */
    public Context appContext;
    /* access modifiers changed from: private */
    public DbHelper mydb;
    SharedPreferences sharedPreferences;

    public Dialfunction(Context context) {
        this.appContext = context;
    }

    public void dialUp(String str, int i, String str2) {
        int i2;
        this.sharedPreferences = this.appContext.getSharedPreferences("serv", 0);
        int i3 = this.sharedPreferences.getInt("dc", 0);
        this.mydb = new DbHelper(this.appContext);
        Cursor data = this.mydb.getData(str2);
        if (data.getCount() > 0) {
            try {
                i2 = Integer.parseInt(data.getString(data.getColumnIndex(DbHelper.CONTACTS_COLUMN_ID)));
            } catch (NumberFormatException unused) {
                i2 = 0;
            }
            this.mydb.updateinfo(Integer.valueOf(i2), "status", "4");
            SavePreferences("oid", str2, this.appContext);
            int i4 = data.getInt(data.getColumnIndex("status"));
            Context context = this.appContext;
            Toast.makeText(context, "Response " + i2, Toast.LENGTH_SHORT).show();
            if (i4 == 0) {
                SharedPreferences.Editor edit = this.sharedPreferences.edit();
                edit.putInt("busy", 1);
                edit.commit();
                if (i3 == 0) {
                    dialUp1(str, i);
                } else {
                    dialUp2(str, i);
                }
            } else {
                Toast.makeText(this.appContext, "Already done this Request", Toast.LENGTH_SHORT).show();
            }
        }
        data.close();
    }

    public void dialUp1(String str, int i) {
        int i2 = i - 1;
        Log.d("USSD", "ussdPhoneNumber" + str);
        String encode = Uri.encode("#");
        if (encode != null) {
            str = str.replace("#", encode);
        }
        StringBuilder sb = new StringBuilder();
        sb.append(Uri.parse("tel:" + str));
        sb.append(Uri.encode("#"));
        this.appContext.startActivity(getActionCallIntent(Uri.parse(sb.toString()), i2));
    }

    public void dialUp2(String number, int simSlot) {
        try {
            String encodedHash = Uri.encode("#");
            String ussdCode = "tel:" + number + encodedHash;
            Intent callIntent = new Intent(Intent.ACTION_CALL);
            callIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            callIntent.setData(Uri.parse(ussdCode));

            // Safely get the account handle
            if (simSlot > 0 && simSlot <= getAccountHandles().size()) {
                callIntent.putExtra("android.telecom.extra.PHONE_ACCOUNT_HANDLE",
                        (Parcelable) getAccountHandles().get(simSlot - 1));
            }

            appContext.startActivity(callIntent);
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(appContext, "Error on function 2", Toast.LENGTH_SHORT).show();
        }
    }


    private List getAccountHandles() {
        try {
            Class<?> cls = Class.forName("android.telecom.TelecomManager");
            Method method = cls.getMethod(Constants.MessagePayloadKeys.FROM, new Class[]{Context.class});
            Object[] objArr = {this};
            return (List) cls.getMethod("getCallCapablePhoneAccounts", new Class[0]).invoke((TelecomManager) method.invoke((Object) null, objArr), new Object[0]);
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this.appContext, "Error on function 1", 0).show();
            return null;
        }
    }

    private Intent getActionCallIntent(Uri uri, int simSlot) {
        Intent intent = new Intent(Intent.ACTION_CALL, uri);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        intent.putExtra("com.android.phone.force.slot", true);
        intent.putExtra("Cdma_Supp", true);

        String[] simSlotKeys = {
                "extra_asus_dial_use_dualsim", "com.android.phone.extra.slot", "slot", "simslot",
                "sim_slot", "subscription", "Subscription", "phone", "com.android.phone.DialingMode",
                "simSlot", "slot_id", "simId", "simnum", "phone_type", "slotId", "slotIdx"
        };

        for (String key : simSlotKeys) {
            intent.putExtra(key, simSlot);
        }

        TelecomManager telecomManager = (TelecomManager) appContext.getSystemService(Context.TELECOM_SERVICE);
        if (telecomManager != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ActivityCompat.checkSelfPermission(appContext, Manifest.permission.READ_PHONE_STATE)
                    == PackageManager.PERMISSION_GRANTED) {
                List<PhoneAccountHandle> handles = telecomManager.getCallCapablePhoneAccounts();
                if (simSlot >= 0 && simSlot < handles.size()) {
                    intent.putExtra(TelecomManager.EXTRA_PHONE_ACCOUNT_HANDLE, handles.get(simSlot));
                }
            }
        }

        return intent;
    }

    public void SavePreferences(String str, String str2, Context context) {
        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(context).edit();
        edit.putString(str, str2);
        edit.commit();
    }

    public void runresupdate(final String str, String str2) {
        // Fix the domain path - remove /mitload if present and use correct base path
        // Use Config class for URL construction
        String protocol = MainActivity.getPref(Config.PREF_KEY_API_PROTOCOL, this.appContext);
        String domain = MainActivity.getPref(Config.PREF_KEY_API_DOMAIN, this.appContext);
        String str3 = Config.buildApiBaseUrl(protocol, domain);
        Log.d("job", "s" + str3);
        ((Responseupdate) NetworkClient.getRetrofitClient(str3).create(Responseupdate.class)).getresupdate(MainActivity.getPref("pin", this.appContext), str, str2, "refot").enqueue(new Callback() {
            public void onResponse(Call call, Response response) {
                if (response.body() != null) {
                    WResponse wResponse = (WResponse) response.body();
                    wResponse.getstatus();
                    if (wResponse.getstatus().intValue() == 1) {
                        Dialfunction dialfunction = Dialfunction.this;
                        DbHelper unused = dialfunction.mydb = new DbHelper(dialfunction.appContext);
                        Dialfunction.this.mydb.remresponse(str);
                        Dialfunction dialfunction2 = Dialfunction.this;
                        dialfunction2.sharedPreferences = dialfunction2.appContext.getSharedPreferences("serv", 0);
                        SharedPreferences.Editor edit = Dialfunction.this.sharedPreferences.edit();
                        edit.putInt("busy", 0);
                        edit.commit();
                    }
                }
                Log.d("job", "ussdPhoneNumber" + response.code());
            }

            public void onFailure(Call call, Throwable th) {
                Log.d("job", Constants.IPC_BUNDLE_KEY_SEND_ERROR + th.toString());
            }
        });
    }

    /**
     * Send USSD response to server
     */
    public void sendResponse(String orderId, String response, String status) {
        try {
            Log.d("Dialfunction", "Sending response for order: " + orderId + ", status: " + status);

            // Use the existing runresupdate method to send response
            runresupdate(orderId, response);

        } catch (Exception e) {
            Log.e("Dialfunction", "Error sending response", e);
        }
    }
}
