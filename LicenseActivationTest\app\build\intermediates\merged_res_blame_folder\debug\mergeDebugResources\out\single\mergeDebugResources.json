[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "com.appystore.mrecharge.app-debug-47:/layout_config.xml.flat", "source": "com.appystore.mrecharge.app-main-49:/layout/config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\layout_license_activation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\layout\\license_activation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_appystoremrecharge.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\appystoremrecharge.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_modern_toggle_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\modern_toggle_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_modern_toggle_off.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\modern_toggle_off.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\layout_settingsd.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\layout\\settingsd.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_footer_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\footer_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_modern_toggle_on.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\modern_toggle_on.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_rounded_cornerss.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\rounded_cornerss.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\layout_item_todo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\layout\\item_todo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\layout_forward.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\layout\\forward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\ic_launcher_background.xml"}, {"merged": "com.appystore.mrecharge.app-debug-47:/layout_settingsd.xml.flat", "source": "com.appystore.mrecharge.app-main-49:/layout/settingsd.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_bg.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\bg.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_modern_edit_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\modern_edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_toggle_on.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\toggle_on.png"}, {"merged": "com.appystore.mrecharge.app-debug-47:/drawable_bgg.xml.flat", "source": "com.appystore.mrecharge.app-main-49:/drawable/bgg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_ic_settings_cell_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\ic_settings_cell_black_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_ic_baseline_add_to_queue_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\ic_baseline_add_to_queue_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_nav_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\nav_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_save.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\save.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_modern_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\modern_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_toggle_off.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\toggle_off.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_modern_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\modern_status_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_ic_home_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\ic_home_black_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_ic_stat_name.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\ic_stat_name.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\menu_bottom_navigation_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\menu\\bottom_navigation_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_toggle_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\toggle_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\layout_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\layout\\config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\xml_ussd_service.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\xml\\ussd_service.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\layout_custom_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\layout\\custom_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\layout_listmain.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\layout\\listmain.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\raw_notification.mp3.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\raw\\notification.mp3"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_rounded_corner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\rounded_corner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\menu_menu_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\menu\\menu_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_select.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\select.xml"}, {"merged": "com.appystore.mrecharge.app-debug-47:/layout_forward.xml.flat", "source": "com.appystore.mrecharge.app-main-49:/layout/forward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_rightorder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\rightorder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-debug-47:\\drawable_ic_settings_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14\\com.appystore.mrecharge.app-main-49:\\drawable\\ic_settings_black_24dp.xml"}, {"merged": "com.appystore.mrecharge.app-debug-47:/layout_activity_main.xml.flat", "source": "com.appystore.mrecharge.app-main-49:/layout/activity_main.xml"}]