package com.appystore.mrecharge.util;

import com.appystore.mrecharge.app.Config;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

/**
 * Message update interface
 * Uses centralized endpoint configuration from Config class
 */
public interface Msgupdate {
    @FormUrlEncoded
    @POST(Config.ENDPOINT_MESSAGE_UPDATE)
    Call<ResponseBody> updateSms(
        @Field("pass") String password,
        @Field("sender") String sender,
        @Field("body") String message,
        @Field("ref") String reference
    );
}
