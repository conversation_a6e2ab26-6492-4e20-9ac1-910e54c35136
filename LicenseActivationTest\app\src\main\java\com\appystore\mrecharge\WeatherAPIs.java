package com.appystore.mrecharge;

import com.appystore.mrecharge.app.Config;
import retrofit2.Call;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

/**
 * Weather APIs interface for service requests
 * Uses centralized endpoint configuration from Config class
 */
public interface WeatherAPIs {
    @FormUrlEncoded
    @POST(Config.ENDPOINT_SERVICE_REQUEST)
    Call<WResponse> getWeatherByCity(@Field("m1") String str, @Field("m2") String str2, @Field("m3") String str3, @Field("m4") String str4, @Field("m5") String str5, @Field("m6") String str6, @Field("m7") String str7, @Field("m8") String str8, @Field("ref") String str9, @Field("pin") String str10, @Field("busy") int i, @Field("id") String str11);
}
