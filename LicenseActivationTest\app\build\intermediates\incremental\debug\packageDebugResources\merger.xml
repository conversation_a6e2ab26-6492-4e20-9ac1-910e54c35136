<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res"><file name="appystoremrecharge" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\appystoremrecharge.png" qualifiers="" type="drawable"/><file name="bg" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\bg.png" qualifiers="" type="drawable"/><file name="button_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="card_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\card_background.xml" qualifiers="" type="drawable"/><file name="gradient_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\gradient_background.xml" qualifiers="" type="drawable"/><file name="ic_baseline_add_to_queue_24" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_baseline_add_to_queue_24.xml" qualifiers="" type="drawable"/><file name="ic_home_black_24dp" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_home_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_settings_black_24dp" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_settings_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_settings_cell_black_24dp" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_settings_cell_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_stat_name" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\ic_stat_name.png" qualifiers="" type="drawable"/><file name="input_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\input_background.xml" qualifiers="" type="drawable"/><file name="modern_button_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_button_background.xml" qualifiers="" type="drawable"/><file name="modern_edit_text_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_edit_text_background.xml" qualifiers="" type="drawable"/><file name="modern_status_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_status_background.xml" qualifiers="" type="drawable"/><file name="modern_toggle_off" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_toggle_off.xml" qualifiers="" type="drawable"/><file name="modern_toggle_on" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_toggle_on.xml" qualifiers="" type="drawable"/><file name="modern_toggle_selector" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\modern_toggle_selector.xml" qualifiers="" type="drawable"/><file name="nav_item_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\nav_item_background.xml" qualifiers="" type="drawable"/><file name="rightorder" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\rightorder.xml" qualifiers="" type="drawable"/><file name="rounded_corner" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\rounded_corner.xml" qualifiers="" type="drawable"/><file name="rounded_cornerss" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\rounded_cornerss.xml" qualifiers="" type="drawable"/><file name="save" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\save.xml" qualifiers="" type="drawable"/><file name="select" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\select.xml" qualifiers="" type="drawable"/><file name="toggle_off" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\toggle_off.png" qualifiers="" type="drawable"/><file name="toggle_on" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\toggle_on.png" qualifiers="" type="drawable"/><file name="toggle_selector" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\toggle_selector.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="config" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\config.xml" qualifiers="" type="layout"/><file name="custom_dialog" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\custom_dialog.xml" qualifiers="" type="layout"/><file name="forward" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\forward.xml" qualifiers="" type="layout"/><file name="item_todo" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\item_todo.xml" qualifiers="" type="layout"/><file name="license_activation" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\license_activation.xml" qualifiers="" type="layout"/><file name="listmain" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\listmain.xml" qualifiers="" type="layout"/><file name="settingsd" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\layout\settingsd.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="menu_main" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\menu\menu_main.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="notification" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\raw\notification.mp3" qualifiers="" type="raw"/><file path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="waiting">#FFAA00</color><color name="done">#00CC00</color><color name="colorPrimary">#6200EE</color><color name="failed">#FF0000</color></file><file path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin"/></file><file path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#00C1EC</color></file><file path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">AppyStoreMRecharge</string><string name="accessibility_service_description">This service is required to automatically process USSD codes for mobile recharges. It reads the USSD response screens and sends the appropriate responses.</string></file><file path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.AppyStoreMRecharge" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.AppyStoreMRecharge" parent="Base.Theme.AppyStoreMRecharge"/></file><file path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.AppyStoreMRecharge" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="ussd_service" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\xml\ussd_service.xml" qualifiers="" type="xml"/><file name="footer_background" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\footer_background.xml" qualifiers="" type="drawable"/><file name="bgg" path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\res\drawable\bgg.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>